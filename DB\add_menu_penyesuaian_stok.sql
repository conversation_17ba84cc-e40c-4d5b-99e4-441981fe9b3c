-- Script untuk menambahkan menu Penyesuaian Stok
-- Jalankan script ini setelah tabel penyesuaian_stok dibuat

-- Insert menu Penyesuaian Stok
INSERT INTO tbl_menu (nama_menu, link, icon, urutan, is_active) 
VALUES ('Penyesuaian Stok', 'penyesuaianstok', 'fas fa-exchange-alt', 6, 'Y');

-- Dapatkan ID menu yang baru saja diinsert
SET @menu_id = LAST_INSERT_ID();

-- Insert akses menu untuk semua level user yang ada
INSERT INTO tbl_akses_menu (id_menu, id_level, view)
SELECT @menu_id, id_level, 'Y' FROM tbl_userlevel;

-- <PERSON><PERSON><PERSON><PERSON> hasil
SELECT 'Menu Penyesuaian Stok berhasil ditambahkan dengan ID:' as status, @menu_id as menu_id;
