<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-list text-blue"></i> Data Supplier</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary  add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="tbl_supplier" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Kode</th>
                                    <th>Nama</th>
                                    <th>No Telepon</th>
                                    <th>Email</th>
                                    <th>PIC</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>


<script type="text/javascript">
    var save_method; //for save method string
    var table;

    $(document).ready(function() {

        //datatables
        table = $("#tbl_supplier").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Supplier Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "supplier/ajax_list",
                "type": "POST"
            },

        });

        //set input/textarea/select event when change value, remove class error and remove text help block
        $("input").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("textarea").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("select").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().next().empty();
            $(this).removeClass('is-invalid');
        });

    });

    function reload_table() {
        table.ajax.reload(null, false); //reload datatable ajax
    }

    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
    });


    //delete
    function hapus(id) {
        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: 'Apakah Anda yakin ingin menghapus data supplier ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                // ajax delete data to database
                $.ajax({
                    url: "supplier/delete",
                    type: "POST",
                    dataType: "JSON",
                    data: {
                        id: id
                    },
                    success: function(data) {
                        if (data.status) {
                            reload_table();
                            Swal.fire({
                                title: 'Berhasil!',
                                text: 'Data supplier berhasil dihapus.',
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: 'Gagal menghapus data supplier.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menghapus data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }



    function add() {
        save_method = 'add';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $.ajax({
            url: "supplier/form_input",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body').html(data);

                // Enable kode field dan show tombol generate saat add
                setTimeout(function() {
                    $('[name="kode"]').prop('readonly', false);
                    $('#btn-generate-kode').show();
                }, 100);

                $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                $('.modal-title').text('Add Supplier'); // Set title to Bootstrap modal title

            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error get data from ajax');
            }
        });
        $('#modal_form').modal({
            backdrop: 'static',
            keyboard: false
        }); // show bootstrap modal
        $('.modal-title').text('Add Supplier'); // Set Title to Bootstrap modal title
    }

    function edit(id) {
        save_method = 'update';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $.ajax({
            url: "supplier/form_input",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body').html(data);
                $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                //Ajax Load data from ajax
                $.ajax({
                    url: "supplier/edit/" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('[name="id"]').val(data.id);
                        $('[name="kode"]').val(data.kode);
                        $('[name="nama"]').val(data.nama);
                        $('[name="no_telepon"]').val(data.no_telepon);
                        $('[name="alamat"]').val(data.alamat);
                        $('[name="alamat_kirim"]').val(data.alamat_kirim);
                        $('[name="email"]').val(data.email);
                        $('[name="nama_pic"]').val(data.nama_pic);
                        $('[name="telepon_pic"]').val(data.telepon_pic);
                        $('[name="npwp"]').val(data.npwp);
                        $('[name="no_ktp"]').val(data.no_ktp);
                        $('[name="is_pkp"]').prop('checked', data.is_pkp == 1);
                        $('[name="status_aktif"]').prop('checked', data.status_aktif == 1);

                        // Disable kode field dan hide tombol generate saat edit
                        $('[name="kode"]').prop('readonly', true);
                        $('#btn-generate-kode').hide();

                        $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                        $('.modal-title').text('Edit Supplier'); // Set title to Bootstrap modal title

                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        alert('Error get data from ajax');
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error get data from ajax');
            }
        });
    }

    function save() {
        $('#btnSave').text('saving...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable
        if (save_method == 'add') {
            url = "supplier/insert";
        } else {
            url = "supplier/update";
        }
        var formdata = new FormData($('#form')[0]);
        // ajax adding data to database
        $.ajax({
            url: url,
            type: "POST",
            data: formdata,
            dataType: "JSON",
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {

                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    reload_table();
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Data supplier berhasil disimpan.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    // Tampilkan error di SweetAlert dan navigasi ke tab yang bermasalah
                    handleValidationErrors(data);
                }
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable


            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data supplier.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable

            }
        });
    }
    var loadFile = function(event) {
        var image = document.getElementById('v_image');
        image.src = URL.createObjectURL(event.target.files[0]);
    };

    // Function untuk generate kode otomatis
    function generateKode() {
        $.ajax({
            url: "supplier/generate_kode",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="kode"]').val(data.kode);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat membuat kode supplier otomatis.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    // Function untuk handle validation errors dengan navigasi tab
    function handleValidationErrors(data) {
        // Mapping field ke tab untuk modul supplier
        var fieldToTab = {
            'kode': 'basic',
            'nama': 'basic',
            'no_telepon': 'basic',
            'alamat': 'basic',
            'alamat_kirim': 'basic',
            'email': 'basic',
            'nama_pic': 'contact',
            'telepon_pic': 'contact',
            'npwp': 'document',
            'no_ktp': 'document'
        };

        var errorMessages = [];
        var firstErrorTab = null;

        // Proses setiap error
        for (var i = 0; i < data.inputerror.length; i++) {
            var fieldName = data.inputerror[i];
            var errorMessage = data.error_string[i];

            // Tambahkan class invalid dan pesan error
            $('[name="' + fieldName + '"]').addClass('is-invalid');
            $('[name="' + fieldName + '"]').closest('.kosong').find('.help-block').text(errorMessage);

            // Kumpulkan pesan error untuk SweetAlert
            errorMessages.push('• ' + errorMessage);

            // Tentukan tab pertama yang bermasalah
            if (firstErrorTab === null && fieldToTab[fieldName]) {
                firstErrorTab = fieldToTab[fieldName];
            }
        }

        // Tampilkan semua error dalam SweetAlert
        var allErrors = errorMessages.join('<br>');
        Swal.fire({
            title: 'Gagal Menyimpan Data!',
            html: '<div style="text-align: left;"><strong>Terdapat kesalahan pengisian:</strong><br><br>' + allErrors + '</div>',
            icon: 'error',
            confirmButtonText: 'OK',
            width: '500px'
        });

        // Navigasi ke tab yang bermasalah
        if (firstErrorTab) {
            $('#supplierTab a[href="#' + firstErrorTab + '"]').tab('show');

            // Highlight tab yang bermasalah
            $('#supplierTab a[href="#' + firstErrorTab + '"]').addClass('text-danger');
            setTimeout(function() {
                $('#supplierTab a[href="#' + firstErrorTab + '"]').removeClass('text-danger');
            }, 3000);
        }
    }
</script>



<!-- Bootstrap modal -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content ">

            <div class="modal-header">
                <h3 class="modal-title"></h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

            </div>
            <div class="modal-body" id="modal-body">

            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!-- End Bootstrap modal -->