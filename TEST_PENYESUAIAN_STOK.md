# Test Case Modul Penyesuaian Stok

## Persiapan Testing
1. Pastikan database toko_elektronik sudah running
2. Pastikan tabel penyesuaian_stok sudah dibuat
3. Pastikan menu Penyesuaian Stok sudah ditambahkan
4. <PERSON>gin ke aplikasi dengan user yang memiliki akses

## Test Case 1: Aks<PERSON> Utama
**URL:** `http://localhost/toko_elektronik/penyesuaianstok`

**Expected Result:**
- <PERSON>aman terbuka tanpa error
- Menampilkan tabel data penyesuaian stok
- Tombol "Add" tersedia
- DataTables berfungsi (pagination, search, dll)

## Test Case 2: Form Input - Add Data
**Steps:**
1. Klik tombol "Add"
2. Modal form terbuka
3. Isi semua field yang required
4. <PERSON><PERSON> "Save"

**Test Data:**
- Tanggal: 2025-01-15
- Barang: Pilih dari dropdown
- Gudang: Pilih dari dropdown  
- Qty Awal: 100
- Qty Baru: 95
- Alasan: "Barang rusak ditemukan saat stock opname"

**Expected Result:**
- Form tersimpan tanpa error
- Muncul notifikasi sukses
- Data muncul di tabel
- Kode penyesuaian ter-generate otomatis (PST0003, PST0004, dst)
- Selisih terhitung otomatis (-5)
- Jenis penyesuaian: PENGURANGAN

## Test Case 3: Generate Kode Otomatis
**Steps:**
1. Buka form Add
2. Klik tombol generate kode (ikon refresh)

**Expected Result:**
- Kode ter-generate dengan format PST0001, PST0002, dst
- Kode unik (tidak duplikat)

## Test Case 4: Kalkulasi Selisih Otomatis
**Steps:**
1. Buka form Add
2. Isi Qty Awal: 50
3. Isi Qty Baru: 55

**Expected Result:**
- Selisih otomatis menampilkan: 5.00
- Jenis penyesuaian: PENAMBAHAN (warna hijau)

**Test dengan nilai negatif:**
1. Isi Qty Awal: 100
2. Isi Qty Baru: 90

**Expected Result:**
- Selisih otomatis menampilkan: -10.00
- Jenis penyesuaian: PENGURANGAN (warna merah)

## Test Case 5: Validasi Form
**Test 5a: Field Required**
**Steps:**
1. Buka form Add
2. Kosongkan field required
3. Klik Save

**Expected Result:**
- Muncul error validation
- Field yang error ditandai dengan border merah
- Pesan error spesifik untuk setiap field

**Test 5b: Format Kode**
**Steps:**
1. Isi kode manual dengan format salah: "ABC123"
2. Klik Save

**Expected Result:**
- Error: "Format kode harus PST0001, PST0002, dst."

**Test 5c: Qty Negatif**
**Steps:**
1. Isi Qty Awal: -10
2. Klik Save

**Expected Result:**
- Error: "Qty awal harus berupa angka positif"

## Test Case 6: Edit Data
**Steps:**
1. Klik tombol edit pada salah satu data
2. Modal form terbuka dengan data ter-load
3. Ubah beberapa field
4. Klik Save

**Expected Result:**
- Data ter-update di database
- Perubahan terlihat di tabel
- Kode penyesuaian readonly (tidak bisa diubah)
- Tombol generate kode hidden

## Test Case 7: Delete Data
**Steps:**
1. Klik tombol delete pada salah satu data
2. Konfirmasi penghapusan

**Expected Result:**
- Muncul konfirmasi SweetAlert
- Setelah konfirmasi, data terhapus
- Muncul notifikasi sukses
- Data hilang dari tabel

## Test Case 8: Dropdown Integration
**Test 8a: Dropdown Barang**
**Expected Result:**
- Menampilkan semua barang aktif
- Format: "BRG0001 - Nama Barang"
- Select2 berfungsi (search, clear)

**Test 8b: Dropdown Gudang**
**Expected Result:**
- Menampilkan semua gudang aktif
- Format: "GDG0001 - Nama Gudang"
- Select2 berfungsi (search, clear)

## Test Case 9: DataTables Features
**Test 9a: Search**
**Steps:**
1. Ketik di search box
2. Tekan Enter

**Expected Result:**
- Data ter-filter sesuai keyword
- Search bekerja di semua kolom

**Test 9b: Pagination**
**Expected Result:**
- Pagination berfungsi jika data > 10 rows
- Bisa ganti jumlah data per halaman

**Test 9c: Sorting**
**Steps:**
1. Klik header kolom

**Expected Result:**
- Data ter-sort ascending/descending

## Test Case 10: Responsive Design
**Steps:**
1. Buka di mobile browser atau resize window

**Expected Result:**
- Tabel responsive
- Modal form responsive
- Semua elemen tetap accessible

## Test Case 11: Error Handling
**Test 11a: Database Error**
**Steps:**
1. Matikan MySQL service
2. Coba akses halaman

**Expected Result:**
- Error handling yang graceful
- Pesan error yang informatif

**Test 11b: AJAX Error**
**Steps:**
1. Ubah URL AJAX ke yang salah
2. Coba save data

**Expected Result:**
- Muncul error notification
- Form tidak ter-submit

## Test Case 12: Security
**Test 12a: SQL Injection**
**Steps:**
1. Coba input SQL injection di search box
2. Coba input SQL injection di form field

**Expected Result:**
- Input ter-sanitize
- Tidak ada SQL injection

**Test 12b: XSS**
**Steps:**
1. Input script tag di form field
2. Save data

**Expected Result:**
- Script ter-escape
- Tidak ada XSS execution

## Test Case 13: Performance
**Test dengan data banyak (>1000 records):**

**Expected Result:**
- Halaman load dalam waktu wajar (<3 detik)
- DataTables server-side processing bekerja
- Pagination smooth
- Search responsive

## Checklist Testing

### Functionality
- [ ] Halaman utama load tanpa error
- [ ] Add data berhasil
- [ ] Edit data berhasil  
- [ ] Delete data berhasil
- [ ] Generate kode otomatis
- [ ] Kalkulasi selisih otomatis
- [ ] Dropdown barang berfungsi
- [ ] Dropdown gudang berfungsi

### Validation
- [ ] Required field validation
- [ ] Format kode validation
- [ ] Numeric validation
- [ ] Unique kode validation

### UI/UX
- [ ] Form layout rapi
- [ ] Tab navigation berfungsi
- [ ] Modal responsive
- [ ] Notifikasi muncul
- [ ] Loading state

### Integration
- [ ] Database connection
- [ ] Foreign key constraint
- [ ] Menu navigation
- [ ] User access control

### Performance
- [ ] Page load time acceptable
- [ ] DataTables performance
- [ ] AJAX response time
- [ ] Memory usage normal

## Bug Report Template
Jika menemukan bug, gunakan format berikut:

**Bug ID:** [Unique ID]
**Severity:** [High/Medium/Low]
**Priority:** [High/Medium/Low]
**Module:** Penyesuaian Stok
**Function:** [Function yang bermasalah]
**Steps to Reproduce:**
1. [Step 1]
2. [Step 2]
3. [Step 3]

**Expected Result:** [Apa yang diharapkan]
**Actual Result:** [Apa yang terjadi]
**Environment:** [Browser, OS, PHP version]
**Screenshot:** [Jika ada]
