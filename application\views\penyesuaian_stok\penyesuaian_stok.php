<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-list text-blue"></i> Data Penyesuaian Stok</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="tbl_penyesuaian_stok" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Kode</th>
                                    <th>Tanggal</th>
                                    <th>Barang</th>
                                    <th>Gudang</th>
                                    <th><PERSON><PERSON></th>
                                    <th>Qty Baru</th>
                                    <th>Selisih</th>
                                    <th>Jenis</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal Form -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Penyesuaian Stok</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Form akan dimuat di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    var table;
    var save_method;

    $(document).ready(function() {
        $('.select2').select2();
        
        //datatables
        table = $("#tbl_penyesuaian_stok").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Penyesuaian Stok Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "<?php echo site_url('penyesuaian/ajax_list') ?>",
                "type": "POST"
            },

        });

        //set input/textarea/select event when change value, remove class error and remove text help block
        $("input").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("textarea").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("select").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });

    });

    function add() {
        save_method = 'add';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('#modal_form').modal({
            backdrop: 'static',
            keyboard: false
        }); // show bootstrap modal
        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo site_url('penyesuaian/form_input') ?>",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body').html(data);

                // Enable kode field dan show tombol generate saat add
                setTimeout(function() {
                    $('[name="kode_penyesuaian"]').prop('readonly', false);
                    $('#btn-generate-kode').show();
                    
                    // Set tanggal hari ini
                    var today = new Date().toISOString().split('T')[0];
                    $('[name="tanggal"]').val(today);
                }, 100);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
        $('.modal-title').text('Add Penyesuaian Stok'); // Set title to Bootstrap modal title
    }

    function edit(id) {
        save_method = 'update';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string

        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo site_url('penyesuaian/form_input') ?>",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body').html(data);

                // Disable kode field dan hide tombol generate saat edit
                setTimeout(function() {
                    $('[name="kode_penyesuaian"]').prop('readonly', true);
                    $('#btn-generate-kode').hide();
                }, 100);

                //Ajax Load data from ajax
                $.ajax({
                    url: "<?php echo site_url('penyesuaian/edit/') ?>" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('[name="id"]').val(data.id);
                        $('[name="kode_penyesuaian"]').val(data.kode_penyesuaian);
                        $('[name="tanggal"]').val(data.tanggal);
                        $('[name="id_barang"]').val(data.id_barang).trigger('change');
                        $('[name="id_gudang"]').val(data.id_gudang).trigger('change');
                        $('[name="qty_awal"]').val(data.qty_awal);
                        $('[name="qty_baru"]').val(data.qty_baru);
                        $('[name="alasan"]').val(data.alasan);
                        $('[name="keterangan"]').val(data.keterangan);
                        $('[name="aktif"]').prop('checked', data.aktif == 1);

                        // Update selisih
                        updateSelisih();

                        $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat memuat data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
        $('.modal-title').text('Edit Penyesuaian Stok'); // Set title to Bootstrap modal title
    }

    function save() {
        $('#btnSave').text('saving...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable
        var url;

        if (save_method == 'add') {
            url = "<?php echo site_url('penyesuaian/insert') ?>";
        } else {
            url = "<?php echo site_url('penyesuaian/update') ?>";
        }

        // ajax adding data to database
        $.ajax({
            url: url,
            type: "POST",
            data: $('#form').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    table.ajax.reload(null, false);
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Data penyesuaian stok berhasil disimpan.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').addClass('is-invalid');
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                    }

                    // Show toast notification with detailed errors
                    var errorMessages = data.error_string.join('<br>');
                    Swal.fire({
                        title: 'Gagal Menyimpan!',
                        html: 'Terjadi kesalahan:<br>' + errorMessages,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable
            }
        });
    }

    function hapus(id) {
        Swal.fire({
            title: 'Apakah Anda yakin?',
            text: "Data penyesuaian stok akan dihapus permanen!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                // ajax delete data to database
                $.ajax({
                    url: "<?php echo site_url('penyesuaian/delete') ?>",
                    type: "POST",
                    dataType: "JSON",
                    data: {
                        id: id
                    },
                    success: function(data) {
                        if (data.status) {
                            table.ajax.reload(null, false);
                            Swal.fire({
                                title: 'Terhapus!',
                                text: 'Data penyesuaian stok berhasil dihapus.',
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Gagal menghapus data.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menghapus data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    // Function untuk generate kode otomatis
    function generateKode() {
        $.ajax({
            url: "<?php echo site_url('penyesuaian/generate_kode') ?>",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="kode_penyesuaian"]').val(data.kode);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Gagal generate kode.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    // Function untuk update selisih otomatis
    function updateSelisih() {
        var qtyAwal = parseFloat($('[name="qty_awal"]').val()) || 0;
        var qtyBaru = parseFloat($('[name="qty_baru"]').val()) || 0;
        var selisih = qtyBaru - qtyAwal;

        $('#qty_selisih_display').text(selisih.toFixed(2));

        // Update jenis penyesuaian
        if (selisih >= 0) {
            $('#jenis_penyesuaian_display').text('PENAMBAHAN').removeClass('text-danger').addClass('text-success');
        } else {
            $('#jenis_penyesuaian_display').text('PENGURANGAN').removeClass('text-success').addClass('text-danger');
        }
    }
</script>
