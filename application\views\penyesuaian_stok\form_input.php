<form action="#" id="form" class="form-horizontal">
    <input type="hidden" value="" name="id" />
    <div class="card-body">

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="penyesuaianStokTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab">Data Dasar</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="detail-tab" data-toggle="tab" href="#detail" role="tab">Detail Penyesuaian</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="status-tab" data-toggle="tab" href="#status" role="tab">Keterangan & Status</a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="penyesuaianStokTabContent">

            <!-- Tab Data Dasar -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="kode_penyesuaian" class="col-sm-3 col-form-label">Kode Penyesuaian</label>
                        <div class="col-sm-9 kosong">
                            <div class="input-group">
                                <input type="text" class="form-control" name="kode_penyesuaian" id="kode_penyesuaian" placeholder="Kode akan di-generate otomatis" autocomplete="off">
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary" id="btn-generate-kode" onclick="generateKode()" title="Generate Kode">
                                        <i class="fas fa-sync"></i>
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">Format: PST0001, PST0002, dst. Kosongkan untuk generate otomatis.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="tanggal" class="col-sm-3 col-form-label">Tanggal <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="date" class="form-control" name="tanggal" id="tanggal" required>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="id_barang" class="col-sm-3 col-form-label">Barang <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="id_barang" id="id_barang" required style="width: 100%;">
                                <option value="">-- Pilih Barang --</option>
                                <?php foreach ($barang_list as $barang): ?>
                                    <option value="<?= $barang->id ?>"><?= $barang->kode_barang ?> - <?= $barang->nama_barang ?></option>
                                <?php endforeach; ?>
                            </select>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="id_gudang" class="col-sm-3 col-form-label">Gudang <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="id_gudang" id="id_gudang" required style="width: 100%;">
                                <option value="">-- Pilih Gudang --</option>
                                <?php foreach ($gudang_list as $gudang): ?>
                                    <option value="<?= $gudang->id ?>"><?= $gudang->kode_gudang ?> - <?= $gudang->nama_gudang ?></option>
                                <?php endforeach; ?>
                            </select>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Detail Penyesuaian -->
            <div class="tab-pane fade" id="detail" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="qty_awal" class="col-sm-3 col-form-label">Qty Awal <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="number" class="form-control" name="qty_awal" id="qty_awal" placeholder="Jumlah stok saat ini" required min="0" step="0.01" onchange="updateSelisih()">
                            <small class="form-text text-muted">Jumlah stok yang tercatat saat ini di sistem.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="qty_baru" class="col-sm-3 col-form-label">Qty Baru <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="number" class="form-control" name="qty_baru" id="qty_baru" placeholder="Jumlah stok setelah penyesuaian" required min="0" step="0.01" onchange="updateSelisih()">
                            <small class="form-text text-muted">Jumlah stok yang sebenarnya setelah pengecekan fisik.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Selisih</label>
                        <div class="col-sm-9">
                            <div class="form-control-plaintext">
                                <strong id="qty_selisih_display">0.00</strong>
                                <small class="text-muted ml-2">(Qty Baru - Qty Awal)</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Jenis Penyesuaian</label>
                        <div class="col-sm-9">
                            <div class="form-control-plaintext">
                                <span class="badge badge-secondary" id="jenis_penyesuaian_display">-</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="alasan" class="col-sm-3 col-form-label">Alasan <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <textarea class="form-control" name="alasan" id="alasan" placeholder="Alasan penyesuaian stok" rows="3" required></textarea>
                            <small class="form-text text-muted">Jelaskan alasan mengapa stok perlu disesuaikan.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Keterangan & Status -->
            <div class="tab-pane fade" id="status" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
                        <div class="col-sm-9 kosong">
                            <textarea class="form-control" name="keterangan" id="keterangan" placeholder="Keterangan tambahan (opsional)" rows="4"></textarea>
                            <small class="form-text text-muted">Informasi tambahan mengenai penyesuaian stok ini.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <hr>
                    <h6 class="text-muted">Status</h6>

                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Status Aktif</label>
                        <div class="col-sm-9">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="aktif" id="aktif" value="1" checked>
                                <label class="form-check-label" for="aktif">
                                    Penyesuaian Aktif
                                </label>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>

    </div>
</form>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#modal_form'),
        placeholder: "-- Pilih --",
        allowClear: true
    });
    
    // Update selisih saat form dimuat
    updateSelisih();
});
</script>
