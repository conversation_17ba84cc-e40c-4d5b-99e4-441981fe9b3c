<form action="#" id="form" class="form-horizontal">
    <input type="hidden" value="" name="id" />
    <div class="card-body">

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="penyesuaianStokTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab">Data Dasar</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="detail-tab" data-toggle="tab" href="#detail" role="tab">Detail Penyesuaian</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="status-tab" data-toggle="tab" href="#status" role="tab">Keterangan & Status</a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="penyesuaianStokTabContent">

            <!-- Tab Data Dasar -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="kode_penyesuaian" class="col-sm-3 col-form-label">Kode Penyesuaian</label>
                        <div class="col-sm-9 kosong">
                            <div class="input-group">
                                <input type="text" class="form-control" name="kode_penyesuaian" id="kode_penyesuaian" placeholder="Kode akan di-generate otomatis" autocomplete="off">
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary" id="btn-generate-kode" onclick="generateKode()" title="Generate Kode">
                                        <i class="fas fa-sync"></i>
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">Format: PST0001, PST0002, dst. Kosongkan untuk generate otomatis.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="tanggal" class="col-sm-3 col-form-label">Tanggal <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="date" class="form-control" name="tanggal" id="tanggal" required>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="id_barang" class="col-sm-3 col-form-label">Barang <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <div class="input-group">
                                <select class="form-control select2" name="id_barang" id="id_barang" required style="width: 100%;">
                                    <option value="">-- Pilih Barang --</option>
                                    <?php foreach ($barang_list as $barang): ?>
                                        <option value="<?= $barang->id ?>"><?= $barang->kode_barang ?> - <?= $barang->nama_barang ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-info" id="btn-info-stok" onclick="showStokInfo()" title="Info Stok" disabled>
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                </div>
                            </div>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="id_gudang" class="col-sm-3 col-form-label">Gudang <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="id_gudang" id="id_gudang" required style="width: 100%;">
                                <option value="">-- Pilih Gudang --</option>
                                <?php foreach ($gudang_list as $gudang): ?>
                                    <option value="<?= $gudang->id ?>"><?= $gudang->kode_gudang ?> - <?= $gudang->nama_gudang ?></option>
                                <?php endforeach; ?>
                            </select>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Detail Penyesuaian -->
            <div class="tab-pane fade" id="detail" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="qty_awal" class="col-sm-3 col-form-label">Qty Awal <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <div class="input-group">
                                <input type="number" class="form-control" name="qty_awal" id="qty_awal" placeholder="Jumlah stok saat ini" required min="0" step="0.01" onchange="updateSelisih()" readonly>
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary" id="btn-load-stok" onclick="loadStokTerakhir()" title="Load Stok Terakhir" disabled>
                                        <i class="fas fa-sync"></i>
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">
                                <span id="stok-info">Stok terakhir akan dimuat otomatis setelah memilih barang dan gudang.</span>
                            </small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="qty_baru" class="col-sm-3 col-form-label">Qty Baru <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="number" class="form-control" name="qty_baru" id="qty_baru" placeholder="Jumlah stok setelah penyesuaian" required min="0" step="0.01" onchange="updateSelisih()">
                            <small class="form-text text-muted">Jumlah stok yang sebenarnya setelah pengecekan fisik.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Selisih</label>
                        <div class="col-sm-9">
                            <div class="form-control-plaintext">
                                <strong id="qty_selisih_display">0.00</strong>
                                <small class="text-muted ml-2">(Qty Baru - Qty Awal)</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Jenis Penyesuaian</label>
                        <div class="col-sm-9">
                            <div class="form-control-plaintext">
                                <span class="badge badge-secondary" id="jenis_penyesuaian_display">-</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="alasan" class="col-sm-3 col-form-label">Alasan <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <textarea class="form-control" name="alasan" id="alasan" placeholder="Alasan penyesuaian stok" rows="3" required></textarea>
                            <small class="form-text text-muted">Jelaskan alasan mengapa stok perlu disesuaikan.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Keterangan & Status -->
            <div class="tab-pane fade" id="status" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
                        <div class="col-sm-9 kosong">
                            <textarea class="form-control" name="keterangan" id="keterangan" placeholder="Keterangan tambahan (opsional)" rows="4"></textarea>
                            <small class="form-text text-muted">Informasi tambahan mengenai penyesuaian stok ini.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <hr>
                    <h6 class="text-muted">Status</h6>

                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Status Aktif</label>
                        <div class="col-sm-9">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="aktif" id="aktif" value="1" checked>
                                <label class="form-check-label" for="aktif">
                                    Penyesuaian Aktif
                                </label>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>

    </div>
</form>

<!-- Modal Info Stok -->
<div class="modal fade" id="modal_stok_info" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Informasi Stok Barang</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-stok-body">
                <!-- Content akan dimuat via AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#modal_form'),
        placeholder: "-- Pilih --",
        allowClear: true
    });

    // Update selisih saat form dimuat
    updateSelisih();

    // Event handler untuk perubahan barang
    $('#id_barang').on('change', function() {
        var id_barang = $(this).val();
        if (id_barang) {
            $('#btn-info-stok').prop('disabled', false);
        } else {
            $('#btn-info-stok').prop('disabled', true);
        }
        checkAndLoadStok();
    });

    // Event handler untuk perubahan gudang
    $('#id_gudang').on('change', function() {
        checkAndLoadStok();
    });
});

// Function untuk cek dan load stok otomatis
function checkAndLoadStok() {
    var id_barang = $('#id_barang').val();
    var id_gudang = $('#id_gudang').val();

    if (id_barang && id_gudang) {
        $('#btn-load-stok').prop('disabled', false);
        loadStokTerakhir();
    } else {
        $('#btn-load-stok').prop('disabled', true);
        $('#qty_awal').val('');
        $('#stok-info').text('Stok terakhir akan dimuat otomatis setelah memilih barang dan gudang.');
        updateSelisih();
    }
}

// Function untuk load stok terakhir
function loadStokTerakhir() {
    var id_barang = $('#id_barang').val();
    var id_gudang = $('#id_gudang').val();

    if (!id_barang || !id_gudang) {
        Swal.fire({
            title: 'Peringatan!',
            text: 'Pilih barang dan gudang terlebih dahulu.',
            icon: 'warning',
            confirmButtonText: 'OK'
        });
        return;
    }

    $('#btn-load-stok').html('<i class="fas fa-spinner fa-spin"></i>');
    $('#btn-load-stok').prop('disabled', true);

    $.ajax({
        url: "<?php echo site_url('penyesuaian/get_stok_terakhir') ?>",
        type: "POST",
        data: {
            id_barang: id_barang,
            id_gudang: id_gudang
        },
        dataType: "JSON",
        success: function(data) {
            $('#qty_awal').val(data.qty_terakhir);
            $('#stok-info').html('<strong>Stok terakhir: ' + parseFloat(data.qty_terakhir).toFixed(2) + '</strong> (dimuat otomatis dari sistem)');
            updateSelisih();
        },
        error: function(jqXHR, textStatus, errorThrown) {
            Swal.fire({
                title: 'Error!',
                text: 'Gagal memuat stok terakhir.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        },
        complete: function() {
            $('#btn-load-stok').html('<i class="fas fa-sync"></i>');
            $('#btn-load-stok').prop('disabled', false);
        }
    });
}

// Function untuk show modal info stok
function showStokInfo() {
    var id_barang = $('#id_barang').val();

    if (!id_barang) {
        Swal.fire({
            title: 'Peringatan!',
            text: 'Pilih barang terlebih dahulu.',
            icon: 'warning',
            confirmButtonText: 'OK'
        });
        return;
    }

    $('#modal-stok-body').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
    $('#modal_stok_info').modal('show');

    $.ajax({
        url: "<?php echo site_url('penyesuaian/get_stok_info') ?>",
        type: "POST",
        data: {
            id_barang: id_barang
        },
        dataType: "JSON",
        success: function(data) {
            var html = '<h6>Stok per Gudang:</h6>';
            html += '<div class="table-responsive">';
            html += '<table class="table table-sm table-bordered">';
            html += '<thead class="bg-light"><tr><th>Gudang</th><th>Stok</th><th>Update Terakhir</th></tr></thead>';
            html += '<tbody>';

            if (data.stok_by_gudang.length > 0) {
                data.stok_by_gudang.forEach(function(item) {
                    html += '<tr>';
                    html += '<td>' + item.kode_gudang + ' - ' + item.nama_gudang + '</td>';
                    html += '<td class="text-right">' + parseFloat(item.qty_terakhir).toFixed(2) + '</td>';
                    html += '<td>' + item.updated_at + '</td>';
                    html += '</tr>';
                });
            } else {
                html += '<tr><td colspan="3" class="text-center">Belum ada data stok</td></tr>';
            }

            html += '</tbody></table></div>';

            html += '<h6 class="mt-3">History Movement (5 Terakhir):</h6>';
            html += '<div class="table-responsive">';
            html += '<table class="table table-sm table-bordered">';
            html += '<thead class="bg-light"><tr><th>Tanggal</th><th>Gudang</th><th>Tipe</th><th>In</th><th>Out</th><th>Ref</th></tr></thead>';
            html += '<tbody>';

            if (data.movement_history.length > 0) {
                data.movement_history.forEach(function(item) {
                    html += '<tr>';
                    html += '<td>' + item.tanggal + '</td>';
                    html += '<td>' + item.kode_gudang + '</td>';
                    html += '<td><span class="badge badge-info">' + item.tipe_transaksi + '</span></td>';
                    html += '<td class="text-right">' + parseFloat(item.qty_in).toFixed(2) + '</td>';
                    html += '<td class="text-right">' + parseFloat(item.qty_out).toFixed(2) + '</td>';
                    html += '<td>' + (item.ref_transaksi || '-') + '</td>';
                    html += '</tr>';
                });
            } else {
                html += '<tr><td colspan="6" class="text-center">Belum ada history movement</td></tr>';
            }

            html += '</tbody></table></div>';

            $('#modal-stok-body').html(html);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            $('#modal-stok-body').html('<div class="alert alert-danger">Gagal memuat informasi stok.</div>');
        }
    });
}
</script>
