<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller Penyesuaian Stok
 * Mengatur penyesuaian stok barang di gudang
 */
class Penyesuaian extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_penyesuaian_stok', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        
        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'penyesuaian_stok/penyesuaian_stok', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        $list = $this->Mod_penyesuaian_stok->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $penyesuaian) {
            $no++;
            $row = array();
            $row[] = $penyesuaian->kode_penyesuaian;
            $row[] = date('d/m/Y', strtotime($penyesuaian->tanggal));
            $row[] = $penyesuaian->nama_barang . '<br><small class="text-muted">' . $penyesuaian->kode_barang . '</small>';
            $row[] = $penyesuaian->nama_gudang . '<br><small class="text-muted">' . $penyesuaian->kode_gudang . '</small>';
            $row[] = number_format($penyesuaian->qty_awal, 2);
            $row[] = number_format($penyesuaian->qty_baru, 2);
            
            // Format selisih dengan warna
            $selisih_class = $penyesuaian->qty_selisih >= 0 ? 'text-success' : 'text-danger';
            $selisih_icon = $penyesuaian->qty_selisih >= 0 ? '+' : '';
            $row[] = '<span class="' . $selisih_class . '">' . $selisih_icon . number_format($penyesuaian->qty_selisih, 2) . '</span>';
            
            // Jenis penyesuaian dengan badge
            $jenis_class = $penyesuaian->jenis_penyesuaian == 'PENAMBAHAN' ? 'badge-success' : 'badge-danger';
            $row[] = '<span class="badge ' . $jenis_class . '">' . $penyesuaian->jenis_penyesuaian . '</span>';
            
            // Status aktif
            if ($penyesuaian->aktif == "1") {
                $row[] = '<span class="badge badge-success">Aktif</span>';
            } else {
                $row[] = '<span class="badge badge-secondary">Tidak Aktif</span>';
            }

            // Action buttons
            $row[] = '<a class="btn btn-xs btn-outline-info edit" href="javascript:void(0)" title="Edit" onclick="edit(' . $penyesuaian->id . ')"><i class="fas fa-edit"></i></a>
                      <a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $penyesuaian->id . ')"><i class="fas fa-trash"></i></a>';
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_penyesuaian_stok->count_all(),
            "recordsFiltered" => $this->Mod_penyesuaian_stok->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert()
    {
        $this->_validate();

        // Generate kode otomatis jika tidak diisi
        $kode = $this->input->post('kode_penyesuaian');
        if (empty($kode)) {
            $kode = $this->Mod_penyesuaian_stok->generate_kode();
        }

        // Hitung selisih dan jenis penyesuaian
        $qty_awal = (float) $this->input->post('qty_awal');
        $qty_baru = (float) $this->input->post('qty_baru');
        $qty_selisih = $qty_baru - $qty_awal;
        $jenis_penyesuaian = $qty_selisih >= 0 ? 'PENAMBAHAN' : 'PENGURANGAN';

        $save = array(
            'kode_penyesuaian' => $kode,
            'tanggal' => $this->input->post('tanggal'),
            'id_barang' => $this->input->post('id_barang'),
            'id_gudang' => $this->input->post('id_gudang'),
            'qty_awal' => $qty_awal,
            'qty_baru' => $qty_baru,
            'qty_selisih' => $qty_selisih,
            'jenis_penyesuaian' => $jenis_penyesuaian,
            'alasan' => $this->input->post('alasan'),
            'keterangan' => $this->input->post('keterangan'),
            'user_id' => $this->session->userdata('id_user'),
            'aktif' => $this->input->post('aktif') ? 1 : 0,
        );
        
        $this->Mod_penyesuaian_stok->insert('penyesuaian_stok', $save);
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');
        
        // Hitung selisih dan jenis penyesuaian
        $qty_awal = (float) $this->input->post('qty_awal');
        $qty_baru = (float) $this->input->post('qty_baru');
        $qty_selisih = $qty_baru - $qty_awal;
        $jenis_penyesuaian = $qty_selisih >= 0 ? 'PENAMBAHAN' : 'PENGURANGAN';

        $save = array(
            'tanggal' => $this->input->post('tanggal'),
            'id_barang' => $this->input->post('id_barang'),
            'id_gudang' => $this->input->post('id_gudang'),
            'qty_awal' => $qty_awal,
            'qty_baru' => $qty_baru,
            'qty_selisih' => $qty_selisih,
            'jenis_penyesuaian' => $jenis_penyesuaian,
            'alasan' => $this->input->post('alasan'),
            'keterangan' => $this->input->post('keterangan'),
            'aktif' => $this->input->post('aktif') ? 1 : 0,
        );

        $this->Mod_penyesuaian_stok->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_penyesuaian_stok->get($id);
        echo json_encode($data);
    }

    public function form_input()
    {
        // Load dropdown data untuk form
        $data['barang_list'] = $this->Mod_penyesuaian_stok->get_barang_dropdown();
        $data['gudang_list'] = $this->Mod_penyesuaian_stok->get_gudang_dropdown();
        $this->load->view('penyesuaian_stok/form_input', $data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_penyesuaian_stok->delete($id);
        echo json_encode(array("status" => TRUE));
    }

    public function generate_kode()
    {
        $kode = $this->Mod_penyesuaian_stok->generate_kode();
        echo json_encode(array('kode' => $kode));
    }

    // Get detail barang via AJAX
    public function get_barang_detail()
    {
        $id_barang = $this->input->post('id_barang');
        $data = $this->Mod_penyesuaian_stok->get_barang_detail($id_barang);
        echo json_encode($data);
    }

    // Get detail gudang via AJAX
    public function get_gudang_detail()
    {
        $id_gudang = $this->input->post('id_gudang');
        $data = $this->Mod_penyesuaian_stok->get_gudang_detail($id_gudang);
        echo json_encode($data);
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        $kode = $this->input->post('kode_penyesuaian');
        $id = $this->input->post('id');

        // Validasi kode penyesuaian
        if (empty($kode)) {
            // Kode boleh kosong, akan di-generate otomatis
        } else {
            if (!preg_match('/^PST[0-9]{4}$/', $kode)) {
                $data['inputerror'][] = 'kode_penyesuaian';
                $data['error_string'][] = 'Format kode harus PST0001, PST0002, dst.';
                $data['status'] = FALSE;
            } else if ($this->Mod_penyesuaian_stok->check_kode_exists($kode, $id)) {
                $data['inputerror'][] = 'kode_penyesuaian';
                $data['error_string'][] = 'Kode penyesuaian sudah ada';
                $data['status'] = FALSE;
            }
        }

        // Validasi tanggal
        if (empty($this->input->post('tanggal'))) {
            $data['inputerror'][] = 'tanggal';
            $data['error_string'][] = 'Tanggal wajib diisi';
            $data['status'] = FALSE;
        }

        // Validasi barang
        if (empty($this->input->post('id_barang'))) {
            $data['inputerror'][] = 'id_barang';
            $data['error_string'][] = 'Barang wajib dipilih';
            $data['status'] = FALSE;
        }

        // Validasi gudang
        if (empty($this->input->post('id_gudang'))) {
            $data['inputerror'][] = 'id_gudang';
            $data['error_string'][] = 'Gudang wajib dipilih';
            $data['status'] = FALSE;
        }

        // Validasi qty awal
        $qty_awal = $this->input->post('qty_awal');
        if (empty($qty_awal) && $qty_awal !== '0') {
            $data['inputerror'][] = 'qty_awal';
            $data['error_string'][] = 'Qty awal wajib diisi';
            $data['status'] = FALSE;
        } else if (!is_numeric($qty_awal) || $qty_awal < 0) {
            $data['inputerror'][] = 'qty_awal';
            $data['error_string'][] = 'Qty awal harus berupa angka positif';
            $data['status'] = FALSE;
        }

        // Validasi qty baru
        $qty_baru = $this->input->post('qty_baru');
        if (empty($qty_baru) && $qty_baru !== '0') {
            $data['inputerror'][] = 'qty_baru';
            $data['error_string'][] = 'Qty baru wajib diisi';
            $data['status'] = FALSE;
        } else if (!is_numeric($qty_baru) || $qty_baru < 0) {
            $data['inputerror'][] = 'qty_baru';
            $data['error_string'][] = 'Qty baru harus berupa angka positif';
            $data['status'] = FALSE;
        }

        // Validasi alasan
        if (empty($this->input->post('alasan'))) {
            $data['inputerror'][] = 'alasan';
            $data['error_string'][] = 'Alasan penyesuaian wajib diisi';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }
}
