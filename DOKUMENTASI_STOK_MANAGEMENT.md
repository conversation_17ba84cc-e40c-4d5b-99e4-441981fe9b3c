# Dokumentasi Integrasi Stok Management

## Deskripsi
Sistem stok management terintegrasi yang mengelola pergerakan stok barang di gudang dengan tracking lengkap dan otomatisasi. Sistem ini terdiri dari tabel stok_movement untuk mencatat semua transaksi dan stok_barang untuk menyimpan stok terakhir.

## Struktur Database

### 1. Tabel stok_movement
Mencatat semua pergerakan stok (masuk/keluar) dengan detail lengkap.

```sql
CREATE TABLE stok_movement (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tanggal DATETIME NOT NULL,
    id_barang INT NOT NULL,
    id_gudang INT NOT NULL,
    tipe_transaksi ENUM(
        'pembelian', 'penjualan', 'retur_beli', 'retur_jual',
        'penyesuaian', 'opname', 'transfer_masuk', 'transfer_keluar'
    ) NOT NULL,
    qty_in DECIMAL(12,2) DEFAULT 0,
    qty_out DECIMAL(12,2) DEFAULT 0,
    keterangan VARCHAR(255),
    ref_transaksi VARCHAR(100),
    user_input VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_barang) REFERENCES barang(id),
    FOREIGN KEY (id_gudang) REFERENCES gudang(id)
);
```

### 2. Tabel stok_barang
Menyimpan stok terakhir per kombinasi barang-gudang.

```sql
CREATE TABLE stok_barang (
    id_barang INT NOT NULL,
    id_gudang INT NOT NULL,
    qty_terakhir DECIMAL(12,2) NOT NULL DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id_barang, id_gudang),
    FOREIGN KEY (id_barang) REFERENCES barang(id),
    FOREIGN KEY (id_gudang) REFERENCES gudang(id)
);
```

### 3. Trigger Otomatis
- **trg_update_stok_barang**: Update otomatis stok_barang setelah insert ke stok_movement
- **trg_penyesuaian_to_movement**: Insert otomatis ke stok_movement setelah penyesuaian stok

### 4. Views
- **v_stok_summary**: Ringkasan stok per barang per gudang
- **v_stok_movement_detail**: Detail movement dengan join ke tabel barang dan gudang

## Integrasi dengan Modul Penyesuaian Stok

### Fitur Baru yang Ditambahkan:

#### 1. **Auto-Load Stok Terakhir**
- Qty awal otomatis dimuat dari tabel stok_barang
- Trigger saat memilih kombinasi barang + gudang
- Button refresh untuk reload manual

#### 2. **Info Stok Barang**
- Modal popup dengan informasi lengkap stok
- Menampilkan stok per gudang
- History movement 5 transaksi terakhir
- Button info di samping dropdown barang

#### 3. **Tracking Movement Otomatis**
- Setiap penyesuaian stok otomatis tercatat di stok_movement
- Trigger database yang menangani update stok_barang
- Referensi ke kode penyesuaian

## Method Baru di Model

### Mod_penyesuaian_stok.php

```php
// Get stok terakhir barang di gudang tertentu
function get_stok_terakhir($id_barang, $id_gudang)

// Get semua stok barang di gudang tertentu  
function get_stok_by_gudang($id_gudang)

// Get semua stok barang tertentu di semua gudang
function get_stok_by_barang($id_barang)

// Get history movement stok
function get_stok_movement($id_barang = null, $id_gudang = null, $limit = 50)

// Insert manual ke stok_movement
function insert_stok_movement($data)

// Get summary stok semua barang
function get_stok_summary()
```

## Method Baru di Controller

### Penyesuaian.php

```php
// Get stok terakhir barang di gudang tertentu
public function get_stok_terakhir()

// Get stok movement history
public function get_stok_movement()

// Get stok summary untuk modal info
public function get_stok_info()
```

## Fitur UI/UX

### 1. **Form Penyesuaian Stok**
- Field qty_awal menjadi readonly
- Button refresh untuk load stok terakhir
- Info stok ditampilkan di bawah field
- Button info stok di samping dropdown barang

### 2. **Modal Info Stok**
- Tabel stok per gudang
- History movement dengan badge tipe transaksi
- Responsive design
- Loading state saat fetch data

### 3. **Auto-Loading**
- Stok terakhir dimuat otomatis saat pilih barang + gudang
- Loading spinner saat proses
- Error handling dengan SweetAlert
- Info text yang informatif

## Tipe Transaksi

| Tipe | Deskripsi | Qty In | Qty Out |
|------|-----------|---------|---------|
| pembelian | Pembelian barang | ✓ | - |
| penjualan | Penjualan barang | - | ✓ |
| retur_beli | Retur pembelian | - | ✓ |
| retur_jual | Retur penjualan | ✓ | - |
| penyesuaian | Penyesuaian stok | ✓/- | ✓/- |
| opname | Stock opname | ✓/- | ✓/- |
| transfer_masuk | Transfer masuk | ✓ | - |
| transfer_keluar | Transfer keluar | - | ✓ |

## Workflow Penyesuaian Stok

1. **User memilih barang dan gudang**
2. **Sistem auto-load stok terakhir** dari tabel stok_barang
3. **User input qty baru** dan alasan penyesuaian
4. **Sistem hitung selisih** otomatis
5. **Save penyesuaian** ke tabel penyesuaian_stok
6. **Trigger otomatis** insert ke stok_movement
7. **Trigger otomatis** update stok_barang

## Data Flow

```
[User Input] → [penyesuaian_stok] → [Trigger] → [stok_movement] → [Trigger] → [stok_barang]
```

## Testing

### 1. **Test Auto-Load Stok**
- Pilih barang dan gudang
- Verify qty_awal terisi otomatis
- Test dengan kombinasi yang belum ada stok

### 2. **Test Modal Info Stok**
- Klik button info stok
- Verify data stok per gudang
- Verify history movement

### 3. **Test Penyesuaian**
- Input penyesuaian stok
- Verify data masuk ke stok_movement
- Verify stok_barang terupdate

### 4. **Test Trigger**
- Insert manual ke stok_movement
- Verify stok_barang terupdate otomatis

## Keuntungan Sistem

### 1. **Akurasi Data**
- Stok selalu sinkron dengan movement
- Trigger database memastikan konsistensi
- History lengkap semua transaksi

### 2. **User Experience**
- Auto-load stok mengurangi error input
- Info stok membantu decision making
- Interface yang intuitif

### 3. **Audit Trail**
- Semua movement tercatat lengkap
- Referensi ke transaksi asal
- Timestamp dan user tracking

### 4. **Performa**
- Stok terakhir disimpan terpisah
- Index database untuk query cepat
- View untuk laporan

## Maintenance

### 1. **Backup Data**
- Backup tabel stok_movement secara berkala
- Archive data lama jika perlu

### 2. **Monitoring**
- Monitor performa trigger
- Check konsistensi data berkala

### 3. **Cleanup**
- Archive movement data lama
- Maintain index database

## Future Enhancement

### 1. **Laporan Stok**
- Laporan stok per periode
- Analisis movement pattern
- Alert stok minimum

### 2. **Batch Operations**
- Import/export stok
- Bulk adjustment
- Transfer antar gudang

### 3. **Integration**
- API untuk sistem lain
- Real-time notification
- Mobile app support
