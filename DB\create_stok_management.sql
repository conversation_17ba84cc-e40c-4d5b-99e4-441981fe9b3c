-- Script untuk membuat tabel stok management
-- Jalankan script ini setelah tabel penyesuaian_stok dibuat

-- Tabel untuk mencatat semua pergerakan stok
CREATE TABLE stok_movement (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tanggal DATETIME NOT NULL,
    id_barang INT NOT NULL,
    id_gudang INT NOT NULL,
    tipe_transaksi ENUM(
        'pembelian', 'penjualan', 'retur_beli', 'retur_jual',
        'penyesuaian', 'opname', 'transfer_masuk', 'transfer_keluar'
    ) NOT NULL,
    qty_in DECIMAL(12,2) DEFAULT 0,
    qty_out DECIMAL(12,2) DEFAULT 0,
    keterangan VARCHAR(255),
    ref_transaksi VARCHAR(100), -- misal: kode invoice, nomor nota, kode penyesuaian
    user_input VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_barang) REFERENCES barang(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (id_gudang) REFERENCES gudang(id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Tabel untuk menyimpan stok terakhir per barang per gudang
CREATE TABLE stok_barang (
    id_barang INT NOT NULL,
    id_gudang INT NOT NULL,
    qty_terakhir DECIMAL(12,2) NOT NULL DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id_barang, id_gudang),
    FOREIGN KEY (id_barang) REFERENCES barang(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (id_gudang) REFERENCES gudang(id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Index untuk performa
CREATE INDEX idx_stok_movement_tanggal ON stok_movement(tanggal);
CREATE INDEX idx_stok_movement_barang ON stok_movement(id_barang);
CREATE INDEX idx_stok_movement_gudang ON stok_movement(id_gudang);
CREATE INDEX idx_stok_movement_tipe ON stok_movement(tipe_transaksi);
CREATE INDEX idx_stok_movement_ref ON stok_movement(ref_transaksi);

-- Trigger untuk update otomatis stok_barang setelah insert stok_movement
DELIMITER $$

CREATE TRIGGER trg_update_stok_barang
AFTER INSERT ON stok_movement
FOR EACH ROW
BEGIN
    DECLARE total_qty DECIMAL(12,2);
    
    -- Cek apakah kombinasi barang-gudang sudah ada
    IF EXISTS (
        SELECT 1 FROM stok_barang
        WHERE id_barang = NEW.id_barang AND id_gudang = NEW.id_gudang
    ) THEN
        -- Update qty_terakhir
        UPDATE stok_barang
        SET qty_terakhir = qty_terakhir + NEW.qty_in - NEW.qty_out
        WHERE id_barang = NEW.id_barang AND id_gudang = NEW.id_gudang;
    ELSE
        -- Insert baru jika belum ada
        INSERT INTO stok_barang (id_barang, id_gudang, qty_terakhir)
        VALUES (NEW.id_barang, NEW.id_gudang, NEW.qty_in - NEW.qty_out);
    END IF;
END$$

DELIMITER ;

-- Insert data contoh stok awal untuk testing
-- Pastikan id_barang dan id_gudang sesuai dengan data yang ada
INSERT INTO stok_movement (
    tanggal, id_barang, id_gudang, tipe_transaksi, 
    qty_in, qty_out, keterangan, ref_transaksi, user_input
) VALUES 
-- Stok awal barang ID 1 di gudang ID 4
(NOW(), 1, 4, 'opname', 100.00, 0.00, 'Stok awal sistem', 'INIT001', 'system'),
-- Stok awal barang ID 2 di gudang ID 4  
(NOW(), 2, 4, 'opname', 50.00, 0.00, 'Stok awal sistem', 'INIT002', 'system'),
-- Stok awal barang ID 1 di gudang ID 3
(NOW(), 1, 3, 'opname', 75.00, 0.00, 'Stok awal sistem', 'INIT003', 'system');

-- Trigger untuk update stok_movement saat ada penyesuaian stok
DELIMITER $$

CREATE TRIGGER trg_penyesuaian_to_movement
AFTER INSERT ON penyesuaian_stok
FOR EACH ROW
BEGIN
    DECLARE qty_in_val DECIMAL(12,2) DEFAULT 0;
    DECLARE qty_out_val DECIMAL(12,2) DEFAULT 0;
    
    -- Tentukan qty_in dan qty_out berdasarkan jenis penyesuaian
    IF NEW.jenis_penyesuaian = 'PENAMBAHAN' THEN
        SET qty_in_val = NEW.qty_selisih;
        SET qty_out_val = 0;
    ELSE
        SET qty_in_val = 0;
        SET qty_out_val = ABS(NEW.qty_selisih);
    END IF;
    
    -- Insert ke stok_movement
    INSERT INTO stok_movement (
        tanggal, id_barang, id_gudang, tipe_transaksi,
        qty_in, qty_out, keterangan, ref_transaksi, user_input
    ) VALUES (
        CONCAT(NEW.tanggal, ' ', TIME(NOW())), 
        NEW.id_barang, 
        NEW.id_gudang, 
        'penyesuaian',
        qty_in_val,
        qty_out_val,
        NEW.alasan,
        NEW.kode_penyesuaian,
        CONCAT('user_', COALESCE(NEW.user_id, 0))
    );
END$$

DELIMITER ;

-- View untuk laporan stok per barang per gudang
CREATE VIEW v_stok_summary AS
SELECT 
    sb.id_barang,
    b.kode_barang,
    b.nama_barang,
    sb.id_gudang,
    g.kode_gudang,
    g.nama_gudang,
    sb.qty_terakhir,
    sb.updated_at
FROM stok_barang sb
JOIN barang b ON sb.id_barang = b.id
JOIN gudang g ON sb.id_gudang = g.id
ORDER BY b.nama_barang, g.nama_gudang;

-- View untuk laporan movement stok
CREATE VIEW v_stok_movement_detail AS
SELECT 
    sm.id,
    sm.tanggal,
    b.kode_barang,
    b.nama_barang,
    g.kode_gudang,
    g.nama_gudang,
    sm.tipe_transaksi,
    sm.qty_in,
    sm.qty_out,
    sm.keterangan,
    sm.ref_transaksi,
    sm.user_input,
    sm.created_at
FROM stok_movement sm
JOIN barang b ON sm.id_barang = b.id
JOIN gudang g ON sm.id_gudang = g.id
ORDER BY sm.tanggal DESC, sm.id DESC;
