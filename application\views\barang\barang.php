<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-list text-blue"></i> Data Barang</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary  add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="tbl_barang" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Kode</th>
                                    <th>Nama Barang</th>
                                    <th>Merk</th>
                                    <th>Tipe</th>
                                    <th><PERSON><PERSON><PERSON></th>
                                    <th><PERSON><PERSON></th>
                                    <th><PERSON><PERSON></th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>


<script type="text/javascript">
    var save_method; //for save method string
    var table;

    $(document).ready(function() {
        $('.select2').select2();
        setTimeout(function() {
            $('input[name="barcode"]').focus()
        }, 3000);
        //datatables
        table = $("#tbl_barang").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Barang Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "<?php echo site_url('barang/ajax_list') ?>",
                "type": "POST"
            },

        });

        //set input/textarea/select event when change value, remove class error and remove text help block
        $("input").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("textarea").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("select").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().next().empty();
            $(this).removeClass('is-invalid');
        });

    });

    function reload_table() {
        table.ajax.reload(null, false); //reload datatable ajax
    }

    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
    });


    //delete
    function hapus(id) {
        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: 'Apakah Anda yakin ingin menghapus data barang ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                // ajax delete data to database
                $.ajax({
                    url: "<?php echo site_url('barang/delete'); ?>",
                    type: "POST",
                    dataType: "JSON",
                    data: {
                        id: id
                    },
                    success: function(data) {
                        if (data.status) {
                            reload_table();
                            Swal.fire({
                                title: 'Berhasil!',
                                text: 'Data barang berhasil dihapus.',
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: 'Gagal menghapus data barang.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menghapus data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }



    function add() {
        save_method = 'add';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('#modal_form').modal({
            backdrop: 'static',
            keyboard: false
        }); // show bootstrap modal
        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo site_url('barang/form_input') ?>",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body').html(data);

                // Enable kode field dan show tombol generate saat add
                setTimeout(function() {
                    $('[name="kode_barang"]').prop('readonly', false);
                    $('#btn-generate-kode').show();
                }, 100);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
        $(document).ready(function() {
            $('.select2').select2();
        });
        $('.modal-title').text('Add Barang'); // Set Title to Bootstrap modal title
    }

    function edit(id) {
        save_method = 'update';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $.ajax({
            url: "<?php echo site_url('barang/form_input') ?>",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body').html(data);

                //Ajax Load data from ajax
                $.ajax({
                    url: "<?php echo site_url('barang/edit') ?>/" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('[name="id"]').val(data.id);
                        $('[name="kode_barang"]').val(data.kode_barang);
                        $('[name="nama_barang"]').val(data.nama_barang);
                        $('[name="merk"]').val(data.merk);
                        $('[name="tipe"]').val(data.tipe);
                        $('[name="spesifikasi"]').val(data.spesifikasi);
                        $('[name="satuan_id"]').val(data.satuan_id);
                        $('[name="jenis_pajak_id"]').val(data.jenis_pajak_id);
                        $('[name="stok_minimum"]').val(data.stok_minimum);
                        $('[name="harga_beli"]').val(data.harga_beli);
                        $('[name="harga_jual"]').val(data.harga_jual);
                        $('[name="aktif"]').prop('checked', data.aktif == 1);

                        // Disable kode field dan hide tombol generate saat edit
                        $('[name="kode_barang"]').prop('readonly', true);
                        $('#btn-generate-kode').hide();

                        $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                        $('.modal-title').text('Edit Barang'); // Set title to Bootstrap modal title

                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat memuat data barang.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form edit.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function save() {
        $('#btnSave').text('saving...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable
        if (save_method == 'add') {
            url = "<?php echo site_url('barang/insert') ?>";
        } else {
            url = "<?php echo site_url('barang/update') ?>";
        }
        var formdata = new FormData($('#form')[0]);
        // ajax adding data to database
        $.ajax({
            url: url,
            type: "POST",
            data: formdata,
            dataType: "JSON",
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {

                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    reload_table();
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Data barang berhasil disimpan.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    // Tampilkan error di SweetAlert dan navigasi ke tab yang bermasalah
                    handleValidationErrors(data);
                }
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable


            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data barang.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable

            }
        });
    }
    var loadFile = function(event) {
        var image = document.getElementById('vbarcode');
        image.src = URL.createObjectURL(event.target.files[0]);
    };

    function hanyaAngka(evt) {
        var charCode = (evt.which) ? evt.which : event.keyCode
        if (charCode > 31 && (charCode < 48 || charCode > 57))

            return false;
        return true;
    }

    // Function untuk generate kode otomatis
    function generateKode() {
        $.ajax({
            url: "barang/generate_kode",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="kode_barang"]').val(data.kode);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat membuat kode barang otomatis.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    // Function untuk handle validation errors dengan navigasi tab
    function handleValidationErrors(data) {
        // Mapping field ke tab untuk modul barang
        var fieldToTab = {
            'kode_barang': 'basic',
            'nama_barang': 'basic',
            'merk': 'basic',
            'tipe': 'basic',
            'satuan_id': 'basic',
            'jenis_pajak_id': 'basic',
            'spesifikasi': 'detail',
            'harga_beli': 'pricing',
            'harga_jual': 'pricing',
            'stok_minimum': 'pricing'
        };

        var errorMessages = [];
        var firstErrorTab = null;

        // Proses setiap error
        for (var i = 0; i < data.inputerror.length; i++) {
            var fieldName = data.inputerror[i];
            var errorMessage = data.error_string[i];

            // Tambahkan class invalid dan pesan error
            $('[name="' + fieldName + '"]').addClass('is-invalid');
            $('[name="' + fieldName + '"]').closest('.kosong').find('.help-block').text(errorMessage);

            // Kumpulkan pesan error untuk toast
            errorMessages.push('• ' + errorMessage);

            // Tentukan tab pertama yang bermasalah
            if (firstErrorTab === null && fieldToTab[fieldName]) {
                firstErrorTab = fieldToTab[fieldName];
            }
        }

        // Tampilkan semua error dalam SweetAlert
        var allErrors = errorMessages.join('<br>');
        Swal.fire({
            title: 'Gagal Menyimpan Data!',
            html: '<div style="text-align: left;"><strong>Terdapat kesalahan pengisian:</strong><br><br>' + allErrors + '</div>',
            icon: 'error',
            confirmButtonText: 'OK',
            width: '500px'
        });

        // Navigasi ke tab yang bermasalah
        if (firstErrorTab) {
            $('#barangTab a[href="#' + firstErrorTab + '"]').tab('show');

            // Highlight tab yang bermasalah
            $('#barangTab a[href="#' + firstErrorTab + '"]').addClass('text-danger');
            setTimeout(function() {
                $('#barangTab a[href="#' + firstErrorTab + '"]').removeClass('text-danger');
            }, 3000);
        }
    }

    function barcode(id) {
        $('#form1')[0].reset(); // reset form on modals
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string

        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo site_url('barang/edit') ?>/" + id,
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="id"]').val(data.id);
                $('[name="barcode"]').val(data.kode_barang);
                $('#vcode').text(data.kode_barang)
                var image = "<?php echo base_url('barang/set_barcodePNG/') ?>" + data.kode_barang + '/' + 2 + '/' + 42;
                $("#vbarcode").load(image);
                $('#modal_form1').modal('show'); // show bootstrap modal when complete loaded

            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error get data from ajax');
            }
        });
    }

    function print_barcode() {
        /*  var data = $('#form1').serialize();
          window.open('barang/print_barcode'+data, '_blank');*/
        $.ajax({
            url: 'barang/print_barcode',
            data: $('#form1').serialize(),
            type: 'post',
            dataType: 'html',
            success: function(respon) {
                var doc = window.open();
                doc.document.write(respon);
                doc.print();
            }
        })
    }
</script>



<!-- Bootstrap modal -->
<div class="modal fade" id="modal_form" role="dialog" tabindex="-1" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-xl">
        <div class="modal-content ">

            <div class="modal-header">
                <h3 class="modal-title">Barang Form</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

            </div>
            <div class="modal-body" id="modal-body">

            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!-- End Bootstrap modal -->




<!-- Bootstrap modal -->
<div class="modal fade" id="modal_form1" role="dialog" tabindex="-1" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-md">
        <div class="modal-content ">

            <div class="modal-header">
                <h3 class="modal-title">Barcode</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

            </div>
            <div class="modal-body form">
                <form action="#" id="form1" class="form-horizontal">
                    <input type="hidden" value="" name="id" />
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group row ">
                                    <label for="nama" class="col-sm-4 col-form-label">Barcode</label>
                                    <div class="col-sm-8 kosong">
                                        <input type="text" class="form-control" autofocus name="barcode" id="barcode" placeholder="Barcode">
                                        <span class="help-block"></span>
                                    </div>
                                </div>

                                <div class="form-group row ">
                                    <label for="nama" class="col-sm-4 col-form-label">Jumlah</label>
                                    <div class="col-sm-8 kosong">
                                        <input type="text" class="form-control" name="jumlah" id="jumlah" placeholder="Jumlah">
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                                <div class="form-group row ">
                                    <label for="nama" class="col-sm-4 col-form-label">View Barcode</label>
                                    <div class="col-sm-8 kosong">
                                        <div id="vbarcode">
                                            <span class="vcode"></span>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="print_barcode()" class="btn btn-primary"><i class="fas fa-print"></i> Cetak Barcode </button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div><!-- /.modal -->
<!-- End Bootstrap modal -->