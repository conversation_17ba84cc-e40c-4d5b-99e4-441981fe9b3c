<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Penyesuaian Stok
 * Mengatur data penyesuaian stok barang di gudang
 */
class Mod_penyesuaian_stok extends CI_Model
{
    var $table = 'penyesuaian_stok';
    var $column_search = array(
        'ps.kode_penyesuaian', 
        'ps.tanggal', 
        'b.nama_barang', 
        'g.nama_gudang', 
        'ps.jenis_penyesuaian', 
        'ps.alasan'
    );
    var $column_order = array(
        'ps.id', 
        'ps.kode_penyesuaian', 
        'ps.tanggal', 
        'b.nama_barang', 
        'g.nama_gudang', 
        'ps.qty_awal', 
        'ps.qty_baru', 
        'ps.qty_selisih', 
        'ps.jenis_penyesuaian', 
        'ps.aktif'
    );
    var $order = array('ps.id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('
            ps.id,
            ps.kode_penyesuaian,
            ps.tanggal,
            ps.id_barang,
            ps.id_gudang,
            ps.qty_awal,
            ps.qty_baru,
            ps.qty_selisih,
            ps.jenis_penyesuaian,
            ps.alasan,
            ps.keterangan,
            ps.aktif,
            b.kode_barang,
            b.nama_barang,
            g.kode_gudang,
            g.nama_gudang
        ');
        $this->db->from('penyesuaian_stok ps');
        $this->db->join('barang b', 'ps.id_barang = b.id', 'left');
        $this->db->join('gudang g', 'ps.id_gudang = g.id', 'left');

        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    function count_all()
    {
        $this->db->from('penyesuaian_stok ps');
        $this->db->join('barang b', 'ps.id_barang = b.id', 'left');
        $this->db->join('gudang g', 'ps.id_gudang = g.id', 'left');
        return $this->db->count_all_results();
    }

    function insert($table, $data)
    {
        $insert = $this->db->insert($table, $data);
        return $insert;
    }

    function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
    }

    function get($id)
    {
        $this->db->select('
            ps.*,
            b.kode_barang,
            b.nama_barang,
            g.kode_gudang,
            g.nama_gudang
        ');
        $this->db->from('penyesuaian_stok ps');
        $this->db->join('barang b', 'ps.id_barang = b.id', 'left');
        $this->db->join('gudang g', 'ps.id_gudang = g.id', 'left');
        $this->db->where('ps.id', $id);
        return $this->db->get()->row();
    }

    function delete($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table);
    }

    // Generate kode penyesuaian otomatis
    function generate_kode()
    {
        $this->db->select('kode_penyesuaian');
        $this->db->from($this->table);
        $this->db->like('kode_penyesuaian', 'PST', 'after');
        $this->db->order_by('kode_penyesuaian', 'DESC');
        $this->db->limit(1);
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            $last_kode = $query->row()->kode_penyesuaian;
            $number = (int) substr($last_kode, 3) + 1;
        } else {
            $number = 1;
        }

        return 'PST' . str_pad($number, 4, '0', STR_PAD_LEFT);
    }

    // Cek apakah kode sudah ada
    function check_kode_exists($kode, $id = null)
    {
        $this->db->where('kode_penyesuaian', $kode);
        if ($id) {
            $this->db->where('id !=', $id);
        }
        $query = $this->db->get($this->table);
        return $query->num_rows() > 0;
    }

    // Get dropdown barang
    function get_barang_dropdown()
    {
        $this->db->select('id, kode_barang, nama_barang');
        $this->db->from('barang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_barang', 'ASC');
        $query = $this->db->get();
        return $query->result();
    }

    // Get dropdown gudang
    function get_gudang_dropdown()
    {
        $this->db->select('id, kode_gudang, nama_gudang');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang', 'ASC');
        $query = $this->db->get();
        return $query->result();
    }

    // Get detail barang untuk form
    function get_barang_detail($id_barang)
    {
        $this->db->select('id, kode_barang, nama_barang, merk, tipe');
        $this->db->from('barang');
        $this->db->where('id', $id_barang);
        $this->db->where('aktif', 1);
        return $this->db->get()->row();
    }

    // Get detail gudang untuk form
    function get_gudang_detail($id_gudang)
    {
        $this->db->select('id, kode_gudang, nama_gudang');
        $this->db->from('gudang');
        $this->db->where('id', $id_gudang);
        $this->db->where('aktif', 1);
        return $this->db->get()->row();
    }
}
