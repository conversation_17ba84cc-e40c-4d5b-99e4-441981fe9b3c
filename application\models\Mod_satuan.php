<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Mod_satuan extends CI_Model
{
	var $table = 'satuan';
	var $column_search = array('kode_satuan', 'nama_satuan', 'keterangan');
	var $column_order = array('id', 'kode_satuan', 'nama_satuan', 'keterangan', 'aktif');
	var $order = array('id' => 'desc');

	function __construct()
	{
		parent::__construct();
		$this->load->database();
	}

	private function _get_datatables_query()
	{

		$this->db->from('satuan');
		$i = 0;

		foreach ($this->column_search as $item) // loop column
		{
			if ($_POST['search']['value']) // if datatable send POST for search
			{

				if ($i === 0) // first loop
				{
					$this->db->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
					$this->db->like($item, $_POST['search']['value']);
				} else {
					$this->db->or_like($item, $_POST['search']['value']);
				}

				if (count($this->column_search) - 1 == $i) //last loop
					$this->db->group_end(); //close bracket
			}
			$i++;
		}

		if (isset($_POST['order'])) // here order processing
		{
			$this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
		} else if (isset($this->order)) {
			$order = $this->order;
			$this->db->order_by(key($order), $order[key($order)]);
		}
	}

	function get_datatables()
	{
		$this->_get_datatables_query();
		if ($_POST['length'] != -1)
			$this->db->limit($_POST['length'], $_POST['start']);
		$query = $this->db->get();
		return $query->result();
	}

	function count_filtered()
	{
		$this->_get_datatables_query();
		$query = $this->db->get();
		return $query->num_rows();
	}

	function count_all()
	{

		$this->db->from('satuan');
		return $this->db->count_all_results();
	}

	function insert($table, $data)
	{
		$insert = $this->db->insert($table, $data);
		return $insert;
	}

	function update($id, $data)
	{
		$this->db->where('id', $id);
		$this->db->update('satuan', $data);
	}

	function get($id)
	{
		$this->db->where('id', $id);
		return $this->db->get('satuan')->row();
	}

	function delete($id)
	{
		$this->db->where('id', $id);
		return $this->db->delete($this->table);
	}

	// Cek apakah kode sudah ada
	function check_kode_exists($kode, $id = null)
	{
		$this->db->where('kode_satuan', $kode);
		if ($id) {
			$this->db->where('id !=', $id);
		}
		$query = $this->db->get($this->table);
		return $query->num_rows() > 0;
	}

	// Get satuan aktif saja
	function get_active_satuan()
	{
		$this->db->where('aktif', 1);
		$this->db->order_by('nama_satuan', 'ASC');
		return $this->db->get($this->table);
	}

	// Update status aktif
	function update_status($id, $status)
	{
		$data = array('aktif' => $status);
		$this->db->where('id', $id);
		return $this->db->update($this->table, $data);
	}
}
