-- <PERSON>ript untuk membuat tabel penyesuaian_stok
-- Jalankan script ini di database MySQL

CREATE TABLE penyesuaian_stok (
    id INT AUTO_INCREMENT PRIMARY KEY,
    kode_penyesuaian VARCHAR(50) NOT NULL UNIQUE,
    tanggal DATE NOT NULL,
    id_barang INT NOT NULL,
    id_gudang INT NOT NULL,
    qty_awal DECIMAL(10,2) NOT NULL,
    qty_baru DECIMAL(10,2) NOT NULL,
    qty_selisih DECIMAL(10,2) NOT NULL,
    jenis_penyesuaian ENUM('PENAMBAHAN', 'PENGURANGAN') NOT NULL,
    alasan TEXT,
    keterangan TEXT,
    user_id INT,
    aktif TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (id_barang) REFERENCES barang(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (id_gudang) REFERENCES gudang(id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Index untuk performa
CREATE INDEX idx_penyesuaian_stok_tanggal ON penyesuaian_stok(tanggal);
CREATE INDEX idx_penyesuaian_stok_barang ON penyesuaian_stok(id_barang);
CREATE INDEX idx_penyesuaian_stok_gudang ON penyesuaian_stok(id_gudang);
CREATE INDEX idx_penyesuaian_stok_kode ON penyesuaian_stok(kode_penyesuaian);

-- Insert data contoh (opsional)
INSERT INTO penyesuaian_stok (
    kode_penyesuaian, 
    tanggal, 
    id_barang, 
    id_gudang, 
    qty_awal, 
    qty_baru, 
    qty_selisih, 
    jenis_penyesuaian, 
    alasan, 
    keterangan
) VALUES 
(
    'PST0001', 
    '2025-01-15', 
    1, 
    4, 
    100.00, 
    95.00, 
    -5.00, 
    'PENGURANGAN', 
    'Barang rusak ditemukan saat stock opname', 
    'Penyesuaian stok bulanan'
),
(
    'PST0002', 
    '2025-01-15', 
    2, 
    4, 
    50.00, 
    55.00, 
    5.00, 
    'PENAMBAHAN', 
    'Barang ditemukan di area yang tidak tercatat', 
    'Koreksi stok setelah audit'
);
