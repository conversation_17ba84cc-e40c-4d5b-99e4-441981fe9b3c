<form action="#" id="form" class="form-horizontal">
    <input type="hidden" value="" name="id" />
    <div class="card-body">

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="satuanTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab">Data Dasar</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="detail-tab" data-toggle="tab" href="#detail" role="tab">Detail & Status</a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="satuanTabContent">

            <!-- Tab Data Dasar -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="kode_satuan" class="col-sm-3 col-form-label">Kode <PERSON> <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="text" class="form-control" name="kode_satuan" id="kode_satuan" placeholder="Kode Satuan (3-10 huruf/angka, unik)" required maxlength="10" minlength="3" autocomplete="off" pattern="[A-Z0-9]{3,10}">
                            <small class="form-text text-muted">Hanya huruf & angka, 3-10 karakter, wajib unik, otomatis UPPERCASE.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="nama_satuan" class="col-sm-3 col-form-label">Nama Satuan <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="text" class="form-control" name="nama_satuan" id="nama_satuan" placeholder="Nama Satuan" required>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Detail & Status -->
            <div class="tab-pane fade" id="detail" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
                        <div class="col-sm-9 kosong">
                            <textarea class="form-control" name="keterangan" id="keterangan" placeholder="Keterangan atau deskripsi satuan" rows="4"></textarea>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <hr>
                    <h6 class="text-muted">Status</h6>

                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Status Aktif</label>
                        <div class="col-sm-9">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="aktif" id="aktif" value="1" checked>
                                <label class="form-check-label" for="aktif">
                                    Satuan Aktif
                                </label>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>

    </div>
</form>
<script>
// Enforce uppercase, alphanumeric, and length on kode_satuan
$(document).ready(function() {
    $('#kode_satuan').on('input', function() {
        let val = $(this).val().toUpperCase().replace(/[^A-Z0-9]/g, '');
        if (val.length > 10) val = val.substr(0, 10);
        $(this).val(val);
    });
});
</script>
