# Dokumentasi Modul Penyesuaian Stok

## Deskripsi
Modul Penyesuaian Stok adalah fitur untuk mengelola penyesuaian stok barang di gudang. Modul ini memungkinkan pengguna untuk mencatat perubahan stok barang yang terjadi karena berbagai alasan seperti kerusakan, kehilangan, atau penemuan barang yang tidak tercatat.

## Fitur Utama
1. **CRUD Lengkap** - Create, Read, Update, Delete data penyesuaian stok
2. **Generate Kode Otomatis** - Kode penyesuaian (PST0001, PST0002, dst.) dibuat otomatis
3. **Integrasi dengan Modul Barang dan Gudang** - Dropdown untuk memilih barang dan gudang
4. **Kalkulasi Otomatis** - Selisih qty dan jenis penyesuaian dihitung otomatis
5. **Validasi Komprehensif** - Validasi input data dengan pesan error yang jelas
6. **Notifikasi SweetAlert** - Notifikasi sukses/error menggunakan SweetAlert
7. **DataTables Server-side** - Tabel data dengan pagination dan pencarian
8. **Form Multi-tab** - Form input dengan layout tab yang rapi

## Struktur Database

### Tabel: penyesuaian_stok
```sql
CREATE TABLE penyesuaian_stok (
    id INT AUTO_INCREMENT PRIMARY KEY,
    kode_penyesuaian VARCHAR(50) NOT NULL UNIQUE,
    tanggal DATE NOT NULL,
    id_barang INT NOT NULL,
    id_gudang INT NOT NULL,
    qty_awal DECIMAL(10,2) NOT NULL,
    qty_baru DECIMAL(10,2) NOT NULL,
    qty_selisih DECIMAL(10,2) NOT NULL,
    jenis_penyesuaian ENUM('PENAMBAHAN', 'PENGURANGAN') NOT NULL,
    alasan TEXT,
    keterangan TEXT,
    user_id INT,
    aktif TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_barang) REFERENCES barang(id),
    FOREIGN KEY (id_gudang) REFERENCES gudang(id)
);
```

### Index untuk Performa
- `idx_penyesuaian_stok_tanggal` - Index pada kolom tanggal
- `idx_penyesuaian_stok_barang` - Index pada kolom id_barang
- `idx_penyesuaian_stok_gudang` - Index pada kolom id_gudang
- `idx_penyesuaian_stok_kode` - Index pada kolom kode_penyesuaian

## File yang Dibuat

### 1. Database
- `DB/create_penyesuaian_stok.sql` - Script untuk membuat tabel dan data contoh

### 2. Controller
- `application/controllers/PenyesuaianStok.php` - Controller utama modul

### 3. Model
- `application/models/Mod_penyesuaian_stok.php` - Model untuk operasi database

### 4. Views
- `application/views/penyesuaian_stok/penyesuaian_stok.php` - Halaman utama
- `application/views/penyesuaian_stok/form_input.php` - Form input/edit

## Cara Penggunaan

### 1. Menambah Data Penyesuaian Stok
1. Klik tombol "Add" di halaman utama
2. Isi form pada tab "Data Dasar":
   - Kode penyesuaian (opsional, akan di-generate otomatis)
   - Tanggal penyesuaian
   - Pilih barang dari dropdown
   - Pilih gudang dari dropdown
3. Isi form pada tab "Detail Penyesuaian":
   - Qty awal (stok saat ini)
   - Qty baru (stok setelah penyesuaian)
   - Alasan penyesuaian (wajib diisi)
4. Isi form pada tab "Keterangan & Status" (opsional):
   - Keterangan tambahan
   - Status aktif
5. Klik "Save"

### 2. Mengedit Data
1. Klik tombol edit (ikon pensil) pada baris data yang ingin diedit
2. Ubah data sesuai kebutuhan
3. Klik "Save"

### 3. Menghapus Data
1. Klik tombol delete (ikon trash) pada baris data yang ingin dihapus
2. Konfirmasi penghapusan
3. Data akan dihapus permanen

## Validasi Input

### Field Wajib
- Tanggal
- Barang
- Gudang
- Qty awal
- Qty baru
- Alasan

### Validasi Khusus
- Kode penyesuaian harus format PST0001, PST0002, dst.
- Qty awal dan qty baru harus berupa angka positif
- Barang dan gudang harus dipilih dari dropdown yang tersedia

## Fitur Otomatis

### 1. Generate Kode
- Kode penyesuaian dibuat otomatis dengan format PST0001, PST0002, dst.
- Tombol generate kode tersedia untuk membuat kode manual

### 2. Kalkulasi Selisih
- Selisih qty dihitung otomatis: qty_baru - qty_awal
- Jenis penyesuaian ditentukan otomatis:
  - PENAMBAHAN jika selisih >= 0
  - PENGURANGAN jika selisih < 0

### 3. Dropdown Dinamis
- Dropdown barang menampilkan barang aktif dengan format: kode - nama
- Dropdown gudang menampilkan gudang aktif dengan format: kode - nama

## Integrasi dengan Modul Lain

### Modul Barang
- Foreign key ke tabel barang
- Dropdown barang mengambil data dari tabel barang yang aktif
- Menampilkan kode dan nama barang

### Modul Gudang
- Foreign key ke tabel gudang
- Dropdown gudang mengambil data dari tabel gudang yang aktif
- Menampilkan kode dan nama gudang

## Keamanan
- Validasi input di sisi server dan client
- Foreign key constraint untuk menjaga integritas data
- Soft delete dengan field aktif
- Session user_id untuk tracking

## Performa
- Index database untuk query yang cepat
- Server-side processing untuk DataTables
- Lazy loading untuk dropdown

## Notifikasi
- SweetAlert untuk konfirmasi dan notifikasi
- Pesan error detail untuk validasi
- Toast notification untuk feedback user

## URL Akses
- Halaman utama: `http://localhost/toko_elektronik/penyesuaianstok`
- Form input: `http://localhost/toko_elektronik/penyesuaianstok/form_input`

## Troubleshooting

### Error Database
- Pastikan tabel penyesuaian_stok sudah dibuat
- Pastikan foreign key ke tabel barang dan gudang valid
- Periksa koneksi database

### Error Form
- Pastikan semua field wajib terisi
- Periksa format kode penyesuaian
- Pastikan dropdown barang dan gudang memiliki data

### Error Permission
- Pastikan user memiliki akses ke modul penyesuaian stok
- Periksa setting menu dan akses user level
